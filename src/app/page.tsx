import Image from "next/image";
import { Bike, <PERSON>, Waves, MapPin, Star, Users, Calendar, ArrowRight } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-orange-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <Bike className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-blue-900">Camí</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
              <a href="#tours" className="text-gray-700 hover:text-blue-600 transition-colors">Tours</a>
              <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors">About</a>
              <a href="#gallery" className="text-gray-700 hover:text-blue-600 transition-colors">Gallery</a>
              <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
            </div>
            <button className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors">
              Book Now
            </button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center">
        {/* Background Image Placeholder */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/90 via-blue-800/80 to-orange-600/70"></div>
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-white">
              <div className="inline-flex items-center bg-orange-500/20 backdrop-blur-sm text-orange-200 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <Star className="h-4 w-4 mr-2" />
                Premium Cycling Adventures
              </div>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                DISCOVER
                <br />
                <span className="text-orange-400">CATALONIA</span>
                <br />
                <span className="text-blue-300">ON TWO WHEELS</span>
              </h1>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl leading-relaxed">
                Experience the authentic beauty of Catalunya through immersive cycling adventures.
                From stunning Mediterranean coastlines to majestic Pyrenees mountains, every pedal stroke tells a story of culture, history, and natural wonder.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button className="bg-orange-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-600 transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl">
                  Explore Tours <ArrowRight className="ml-2 h-5 w-5" />
                </button>
                <button className="border-2 border-white/30 backdrop-blur-sm text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white/10 transition-all duration-300">
                  Watch Video
                </button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-white/20">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">500+</div>
                  <div className="text-sm text-blue-200">Happy Cyclists</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">15+</div>
                  <div className="text-sm text-blue-200">Tour Routes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">5★</div>
                  <div className="text-sm text-blue-200">Average Rating</div>
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image */}
            <div className="relative">
              <div className="relative h-96 md:h-[600px] rounded-2xl overflow-hidden shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-blue-600/30 backdrop-blur-sm"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <Bike className="h-32 w-32 mx-auto mb-6 opacity-90 text-orange-300" />
                    <p className="text-lg font-medium">Cyclists on coastal road in Catalunya</p>
                    <p className="text-sm text-blue-200 mt-2">Photo: Costa Brava cycling route</p>
                  </div>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-4 right-4 w-20 h-20 bg-orange-400/20 rounded-full blur-xl"></div>
                <div className="absolute bottom-4 left-4 w-16 h-16 bg-blue-400/20 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Tours */}
      <section id="tours" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-blue-900 mb-4">Featured Tours</h2>
            <p className="text-xl text-gray-600">Choose your perfect cycling adventure</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Coastal Route */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2">
              <div className="relative mb-6">
                <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <Waves className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  POPULAR
                </div>
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-3">Coastal Route</h3>
              <p className="text-gray-600 text-center mb-6 leading-relaxed">
                Ride along Catalunya's breathtaking Mediterranean coastline, discovering hidden coves and charming fishing villages.
              </p>

              {/* Tour Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Duration:</span>
                  <span className="font-semibold text-gray-700">3-5 days</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Distance:</span>
                  <span className="font-semibold text-gray-700">120-200 km</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Difficulty:</span>
                  <span className="font-semibold text-blue-600">Easy-Medium</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Price from:</span>
                  <span className="font-bold text-orange-600">€299</span>
                </div>
              </div>

              <div className="flex justify-center">
                <button className="bg-blue-600 text-white px-8 py-3 rounded-full hover:bg-blue-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg">
                  Learn More
                </button>
              </div>
            </div>

            {/* Mountain Trail */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2">
              <div className="relative mb-6">
                <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <Mountain className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  CHALLENGE
                </div>
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-3">Mountain Trail</h3>
              <p className="text-gray-600 text-center mb-6 leading-relaxed">
                Challenge yourself on scenic mountain paths through the Pyrenees, with stunning vistas and authentic mountain culture.
              </p>

              {/* Tour Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Duration:</span>
                  <span className="font-semibold text-gray-700">5-7 days</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Distance:</span>
                  <span className="font-semibold text-gray-700">200-350 km</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Difficulty:</span>
                  <span className="font-semibold text-red-600">Hard</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Price from:</span>
                  <span className="font-bold text-orange-600">€499</span>
                </div>
              </div>

              <div className="flex justify-center">
                <button className="bg-orange-600 text-white px-8 py-3 rounded-full hover:bg-orange-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg">
                  Learn More
                </button>
              </div>
            </div>

            {/* Valley Path */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2">
              <div className="relative mb-6">
                <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <MapPin className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  CULTURAL
                </div>
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-3">Valley Path</h3>
              <p className="text-gray-600 text-center mb-6 leading-relaxed">
                Explore peaceful valleys dotted with vineyards, historic towns, and local markets showcasing Catalunya's rich heritage.
              </p>

              {/* Tour Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Duration:</span>
                  <span className="font-semibold text-gray-700">2-4 days</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Distance:</span>
                  <span className="font-semibold text-gray-700">80-150 km</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Difficulty:</span>
                  <span className="font-semibold text-green-600">Easy</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Price from:</span>
                  <span className="font-bold text-orange-600">€199</span>
                </div>
              </div>

              <div className="flex justify-center">
                <button className="bg-green-600 text-white px-8 py-3 rounded-full hover:bg-green-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg">
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">What Our Cyclists Say</h2>
            <p className="text-xl text-blue-200">Real experiences from real adventurers</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-blue-100 mb-6 italic leading-relaxed">
                "An absolutely incredible experience! The coastal route was breathtaking, and our guide Maria was fantastic. Every detail was perfectly organized."
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  MJ
                </div>
                <div>
                  <div className="font-semibold">Michael Johnson</div>
                  <div className="text-blue-300 text-sm">London, UK</div>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-blue-100 mb-6 italic leading-relaxed">
                "The mountain trail challenged me in the best way possible. The views were spectacular and the local cuisine was amazing. Highly recommend!"
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  SL
                </div>
                <div>
                  <div className="font-semibold">Sarah López</div>
                  <div className="text-blue-300 text-sm">Barcelona, Spain</div>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-blue-100 mb-6 italic leading-relaxed">
                "Perfect for families! The valley path was gentle enough for our kids but still exciting. We learned so much about Catalan culture."
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                  DM
                </div>
                <div>
                  <div className="font-semibold">David Martinez</div>
                  <div className="text-blue-300 text-sm">Paris, France</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section id="about" className="py-20 bg-gradient-to-br from-orange-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <Star className="h-4 w-4 mr-2" />
                Zas! Our Story
              </div>
              <h2 className="text-4xl font-bold text-blue-900 mb-6">
                Founded by passionate cyclists, we're dedicated to sharing the beauty of Catalonia through unique and memorable bike tours.
              </h2>
              <p className="text-lg text-gray-700 mb-8">
                "A fantastic way to experience the diverse landscapes of Catalonia!" - Michael T.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-3 mx-auto">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-blue-900">Expert Guides</h4>
                  <p className="text-gray-600 text-sm">Local knowledge & passion</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-600 rounded-full mb-3 mx-auto">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-blue-900">All Skill Levels</h4>
                  <p className="text-gray-600 text-sm">Adventures for everyone</p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-orange-500 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Why Choose Camí?</h3>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Authentic local experiences
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Small group adventures
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Premium equipment included
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Cultural immersion guaranteed
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery */}
      <section id="gallery" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-blue-900 mb-4">Adventure Gallery</h2>
            <p className="text-xl text-gray-600">Moments captured from our cycling tours</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Gallery Items - Using placeholder gradients */}
            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Waves className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Costa Brava</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-orange-400 to-red-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Mountain className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Pyrenees</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-green-400 to-green-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <MapPin className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Wine Valley</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Users className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Group Tours</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer md:col-span-2">
              <div className="aspect-[2/1] bg-gradient-to-br from-blue-500 via-purple-500 to-orange-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Bike className="h-12 w-12 mx-auto mb-3" />
                    <p className="text-lg font-medium">Barcelona Highlights</p>
                    <p className="text-sm opacity-80">City cycling tour</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Star className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Sunset Ride</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative group cursor-pointer">
              <div className="aspect-square bg-gradient-to-br from-teal-400 to-blue-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Calendar className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm font-medium">Local Markets</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl">
              View Full Gallery
            </button>
          </div>
        </div>
      </section>

      {/* Contact */}
      <section id="contact" className="py-20 bg-blue-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready for Your Adventure?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join us for an unforgettable cycling experience through the heart of Catalunya. 
            Contact us today to start planning your perfect bike tour.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button className="bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-700 transition-colors">
              Book Your Tour
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors">
              Get In Touch
            </button>
          </div>
          <p className="text-blue-200">
            Email: <EMAIL> | Phone: +34 123 456 789
          </p>
        </div>
      </section>
    </div>
  );
}
