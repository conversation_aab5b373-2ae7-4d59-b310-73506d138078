import Image from "next/image";
import { Bike, <PERSON>, Waves, MapPin, Star, Users, Calendar, ArrowRight } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-orange-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <Bike className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-blue-900">Camí</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
              <a href="#tours" className="text-gray-700 hover:text-blue-600 transition-colors">Tours</a>
              <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors">About</a>
              <a href="#gallery" className="text-gray-700 hover:text-blue-600 transition-colors">Gallery</a>
              <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
            </div>
            <button className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors">
              Book Now
            </button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <section id="home" className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-bold text-blue-900 mb-6">
              DISCOVER CATALONIA
              <br />
              <span className="text-blue-600">ON TWO WHEELS</span>
            </h1>
            <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
              Experience the authentic beauty of Catalunya through immersive cycling adventures. 
              From stunning coastlines to majestic mountains, every pedal stroke tells a story.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                Explore Tours <ArrowRight className="ml-2 h-5 w-5" />
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                Watch Video
              </button>
            </div>
          </div>
        </div>
        
        {/* Hero Image */}
        <div className="mt-16 max-w-6xl mx-auto">
          <div className="relative h-96 md:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-orange-500/20"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <Bike className="h-24 w-24 mx-auto mb-4 opacity-80" />
                <p className="text-lg">Cyclists on coastal road in Catalunya</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Tours */}
      <section id="tours" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-blue-900 mb-4">Featured Tours</h2>
            <p className="text-xl text-gray-600">Choose your perfect cycling adventure</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Coastal Route */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors">
              <div className="flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-6 mx-auto">
                <Waves className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-4">Coastal Route</h3>
              <p className="text-gray-700 text-center mb-6">
                Ride along Catalunya's breathtaking Mediterranean coastline, discovering hidden coves and charming fishing villages.
              </p>
              <div className="flex justify-center">
                <button className="bg-blue-600 text-white px-6 py-3 rounded-full hover:bg-blue-700 transition-colors">
                  Learn More
                </button>
              </div>
            </div>

            {/* Mountain Trail */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors">
              <div className="flex items-center justify-center w-16 h-16 bg-orange-600 rounded-full mb-6 mx-auto">
                <Mountain className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-4">Mountain Trail</h3>
              <p className="text-gray-700 text-center mb-6">
                Challenge yourself on scenic mountain paths through the Pyrenees, with stunning vistas and authentic mountain culture.
              </p>
              <div className="flex justify-center">
                <button className="bg-orange-600 text-white px-6 py-3 rounded-full hover:bg-orange-700 transition-colors">
                  Learn More
                </button>
              </div>
            </div>

            {/* Valley Path */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors">
              <div className="flex items-center justify-center w-16 h-16 bg-green-600 rounded-full mb-6 mx-auto">
                <MapPin className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-blue-900 text-center mb-4">Valley Path</h3>
              <p className="text-gray-700 text-center mb-6">
                Explore peaceful valleys dotted with vineyards, historic towns, and local markets showcasing Catalunya's rich heritage.
              </p>
              <div className="flex justify-center">
                <button className="bg-green-600 text-white px-6 py-3 rounded-full hover:bg-green-700 transition-colors">
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section id="about" className="py-20 bg-gradient-to-br from-orange-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <Star className="h-4 w-4 mr-2" />
                Zas! Our Story
              </div>
              <h2 className="text-4xl font-bold text-blue-900 mb-6">
                Founded by passionate cyclists, we're dedicated to sharing the beauty of Catalonia through unique and memorable bike tours.
              </h2>
              <p className="text-lg text-gray-700 mb-8">
                "A fantastic way to experience the diverse landscapes of Catalonia!" - Michael T.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-3 mx-auto">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-blue-900">Expert Guides</h4>
                  <p className="text-gray-600 text-sm">Local knowledge & passion</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-600 rounded-full mb-3 mx-auto">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-blue-900">All Skill Levels</h4>
                  <p className="text-gray-600 text-sm">Adventures for everyone</p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-orange-500 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Why Choose Camí?</h3>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Authentic local experiences
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Small group adventures
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Premium equipment included
                  </li>
                  <li className="flex items-center">
                    <Star className="h-5 w-5 mr-3 text-yellow-300" />
                    Cultural immersion guaranteed
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact */}
      <section id="contact" className="py-20 bg-blue-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready for Your Adventure?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join us for an unforgettable cycling experience through the heart of Catalunya. 
            Contact us today to start planning your perfect bike tour.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button className="bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-700 transition-colors">
              Book Your Tour
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors">
              Get In Touch
            </button>
          </div>
          <p className="text-blue-200">
            Email: <EMAIL> | Phone: +34 123 456 789
          </p>
        </div>
      </section>
    </div>
  );
}
