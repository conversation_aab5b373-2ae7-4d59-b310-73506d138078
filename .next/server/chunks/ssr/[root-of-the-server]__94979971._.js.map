{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/camiweb/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Bike, <PERSON>, Waves, MapPin, Star, Users, Calendar, ArrowRight } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-orange-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-2\">\n              <Bike className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-2xl font-bold text-blue-900\">Camí</span>\n            </div>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Home</a>\n              <a href=\"#tours\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Tours</a>\n              <a href=\"#about\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">About</a>\n              <a href=\"#gallery\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Gallery</a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Contact</a>\n            </div>\n            <button className=\"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors\">\n              Book Now\n            </button>\n          </div>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"relative min-h-screen flex items-center\">\n        {/* Background Image Placeholder */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/90 via-blue-800/80 to-orange-600/70\"></div>\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            backgroundRepeat: 'repeat'\n          }}></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Left Content */}\n            <div className=\"text-white\">\n              <div className=\"inline-flex items-center bg-orange-500/20 backdrop-blur-sm text-orange-200 px-4 py-2 rounded-full text-sm font-semibold mb-6\">\n                <Star className=\"h-4 w-4 mr-2\" />\n                Premium Cycling Adventures\n              </div>\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n                DISCOVER\n                <br />\n                <span className=\"text-orange-400\">CATALONIA</span>\n                <br />\n                <span className=\"text-blue-300\">ON TWO WHEELS</span>\n              </h1>\n              <p className=\"text-xl text-blue-100 mb-8 max-w-2xl leading-relaxed\">\n                Experience the authentic beauty of Catalunya through immersive cycling adventures.\n                From stunning Mediterranean coastlines to majestic Pyrenees mountains, every pedal stroke tells a story of culture, history, and natural wonder.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 mb-8\">\n                <button className=\"bg-orange-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-600 transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl\">\n                  Explore Tours <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </button>\n                <button className=\"border-2 border-white/30 backdrop-blur-sm text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white/10 transition-all duration-300\">\n                  Watch Video\n                </button>\n              </div>\n\n              {/* Stats */}\n              <div className=\"grid grid-cols-3 gap-6 pt-8 border-t border-white/20\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-400\">500+</div>\n                  <div className=\"text-sm text-blue-200\">Happy Cyclists</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-400\">15+</div>\n                  <div className=\"text-sm text-blue-200\">Tour Routes</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-400\">5★</div>\n                  <div className=\"text-sm text-blue-200\">Average Rating</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Content - Hero Image */}\n            <div className=\"relative\">\n              <div className=\"relative h-96 md:h-[600px] rounded-2xl overflow-hidden shadow-2xl\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-blue-600/30 backdrop-blur-sm\"></div>\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-center text-white\">\n                    <Bike className=\"h-32 w-32 mx-auto mb-6 opacity-90 text-orange-300\" />\n                    <p className=\"text-lg font-medium\">Cyclists on coastal road in Catalunya</p>\n                    <p className=\"text-sm text-blue-200 mt-2\">Photo: Costa Brava cycling route</p>\n                  </div>\n                </div>\n                {/* Decorative elements */}\n                <div className=\"absolute top-4 right-4 w-20 h-20 bg-orange-400/20 rounded-full blur-xl\"></div>\n                <div className=\"absolute bottom-4 left-4 w-16 h-16 bg-blue-400/20 rounded-full blur-xl\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Tours */}\n      <section id=\"tours\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">Featured Tours</h2>\n            <p className=\"text-xl text-gray-600\">Choose your perfect cycling adventure</p>\n          </div>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {/* Coastal Route */}\n            <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2\">\n              <div className=\"relative mb-6\">\n                <div className=\"flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                  <Waves className=\"h-10 w-10 text-white\" />\n                </div>\n                <div className=\"absolute -top-2 -right-2 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                  POPULAR\n                </div>\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-3\">Coastal Route</h3>\n              <p className=\"text-gray-600 text-center mb-6 leading-relaxed\">\n                Ride along Catalunya's breathtaking Mediterranean coastline, discovering hidden coves and charming fishing villages.\n              </p>\n\n              {/* Tour Details */}\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Duration:</span>\n                  <span className=\"font-semibold text-gray-700\">3-5 days</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Distance:</span>\n                  <span className=\"font-semibold text-gray-700\">120-200 km</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Difficulty:</span>\n                  <span className=\"font-semibold text-blue-600\">Easy-Medium</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Price from:</span>\n                  <span className=\"font-bold text-orange-600\">€299</span>\n                </div>\n              </div>\n\n              <div className=\"flex justify-center\">\n                <button className=\"bg-blue-600 text-white px-8 py-3 rounded-full hover:bg-blue-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n\n            {/* Mountain Trail */}\n            <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2\">\n              <div className=\"relative mb-6\">\n                <div className=\"flex items-center justify-center w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                  <Mountain className=\"h-10 w-10 text-white\" />\n                </div>\n                <div className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                  CHALLENGE\n                </div>\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-3\">Mountain Trail</h3>\n              <p className=\"text-gray-600 text-center mb-6 leading-relaxed\">\n                Challenge yourself on scenic mountain paths through the Pyrenees, with stunning vistas and authentic mountain culture.\n              </p>\n\n              {/* Tour Details */}\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Duration:</span>\n                  <span className=\"font-semibold text-gray-700\">5-7 days</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Distance:</span>\n                  <span className=\"font-semibold text-gray-700\">200-350 km</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Difficulty:</span>\n                  <span className=\"font-semibold text-red-600\">Hard</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Price from:</span>\n                  <span className=\"font-bold text-orange-600\">€499</span>\n                </div>\n              </div>\n\n              <div className=\"flex justify-center\">\n                <button className=\"bg-orange-600 text-white px-8 py-3 rounded-full hover:bg-orange-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n\n            {/* Valley Path */}\n            <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-orange-200 hover:-translate-y-2\">\n              <div className=\"relative mb-6\">\n                <div className=\"flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                  <MapPin className=\"h-10 w-10 text-white\" />\n                </div>\n                <div className=\"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                  CULTURAL\n                </div>\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-3\">Valley Path</h3>\n              <p className=\"text-gray-600 text-center mb-6 leading-relaxed\">\n                Explore peaceful valleys dotted with vineyards, historic towns, and local markets showcasing Catalunya's rich heritage.\n              </p>\n\n              {/* Tour Details */}\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Duration:</span>\n                  <span className=\"font-semibold text-gray-700\">2-4 days</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Distance:</span>\n                  <span className=\"font-semibold text-gray-700\">80-150 km</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Difficulty:</span>\n                  <span className=\"font-semibold text-green-600\">Easy</span>\n                </div>\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-500\">Price from:</span>\n                  <span className=\"font-bold text-orange-600\">€199</span>\n                </div>\n              </div>\n\n              <div className=\"flex justify-center\">\n                <button className=\"bg-green-600 text-white px-8 py-3 rounded-full hover:bg-green-700 transition-all duration-300 font-semibold shadow-md hover:shadow-lg\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials */}\n      <section className=\"py-20 bg-gradient-to-br from-blue-900 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">What Our Cyclists Say</h2>\n            <p className=\"text-xl text-blue-200\">Real experiences from real adventurers</p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-blue-100 mb-6 italic leading-relaxed\">\n                \"An absolutely incredible experience! The coastal route was breathtaking, and our guide Maria was fantastic. Every detail was perfectly organized.\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4\">\n                  MJ\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Michael Johnson</div>\n                  <div className=\"text-blue-300 text-sm\">London, UK</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-blue-100 mb-6 italic leading-relaxed\">\n                \"The mountain trail challenged me in the best way possible. The views were spectacular and the local cuisine was amazing. Highly recommend!\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4\">\n                  SL\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Sarah López</div>\n                  <div className=\"text-blue-300 text-sm\">Barcelona, Spain</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <div className=\"flex items-center mb-4\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <p className=\"text-blue-100 mb-6 italic leading-relaxed\">\n                \"Perfect for families! The valley path was gentle enough for our kids but still exciting. We learned so much about Catalan culture.\"\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mr-4\">\n                  DM\n                </div>\n                <div>\n                  <div className=\"font-semibold\">David Martinez</div>\n                  <div className=\"text-blue-300 text-sm\">Paris, France</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Story */}\n      <section id=\"about\" className=\"py-20 bg-gradient-to-br from-orange-50 to-blue-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            <div>\n              <div className=\"inline-flex items-center bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-semibold mb-6\">\n                <Star className=\"h-4 w-4 mr-2\" />\n                Zas! Our Story\n              </div>\n              <h2 className=\"text-4xl font-bold text-blue-900 mb-6\">\n                Founded by passionate cyclists, we're dedicated to sharing the beauty of Catalonia through unique and memorable bike tours.\n              </h2>\n              <p className=\"text-lg text-gray-700 mb-8\">\n                \"A fantastic way to experience the diverse landscapes of Catalonia!\" - Michael T.\n              </p>\n              <div className=\"grid grid-cols-2 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-3 mx-auto\">\n                    <Users className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-blue-900\">Expert Guides</h4>\n                  <p className=\"text-gray-600 text-sm\">Local knowledge & passion</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-orange-600 rounded-full mb-3 mx-auto\">\n                    <Calendar className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-blue-900\">All Skill Levels</h4>\n                  <p className=\"text-gray-600 text-sm\">Adventures for everyone</p>\n                </div>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"bg-gradient-to-br from-blue-600 to-orange-500 rounded-2xl p-8 text-white\">\n                <h3 className=\"text-2xl font-bold mb-4\">Why Choose Camí?</h3>\n                <ul className=\"space-y-3\">\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Authentic local experiences\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Small group adventures\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Premium equipment included\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Cultural immersion guaranteed\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Gallery */}\n      <section id=\"gallery\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">Adventure Gallery</h2>\n            <p className=\"text-xl text-gray-600\">Moments captured from our cycling tours</p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {/* Gallery Items - Using placeholder gradients */}\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Waves className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Costa Brava</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-orange-400 to-red-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Mountain className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Pyrenees</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-green-400 to-green-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <MapPin className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Wine Valley</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Users className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Group Tours</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer md:col-span-2\">\n              <div className=\"aspect-[2/1] bg-gradient-to-br from-blue-500 via-purple-500 to-orange-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Bike className=\"h-12 w-12 mx-auto mb-3\" />\n                    <p className=\"text-lg font-medium\">Barcelona Highlights</p>\n                    <p className=\"text-sm opacity-80\">City cycling tour</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Star className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Sunset Ride</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"relative group cursor-pointer\">\n              <div className=\"aspect-square bg-gradient-to-br from-teal-400 to-blue-500 rounded-xl overflow-hidden shadow-lg group-hover:shadow-2xl transition-all duration-300\">\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center\">\n                  <div className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <Calendar className=\"h-8 w-8 mx-auto mb-2\" />\n                    <p className=\"text-sm font-medium\">Local Markets</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <button className=\"bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl\">\n              View Full Gallery\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact */}\n      <section id=\"contact\" className=\"py-20 bg-blue-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold mb-6\">Ready for Your Adventure?</h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto\">\n            Join us for an unforgettable cycling experience through the heart of Catalunya. \n            Contact us today to start planning your perfect bike tour.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-8\">\n            <button className=\"bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-700 transition-colors\">\n              Book Your Tour\n            </button>\n            <button className=\"border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors\">\n              Get In Touch\n            </button>\n          </div>\n          <p className=\"text-blue-200\">\n            Email: <EMAIL> | Phone: +34 123 456 789\n          </p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAsD;;;;;;kDAChF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACjF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACjF,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACnF,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;0CAErF,8OAAC;gCAAO,WAAU;0CAAoF;;;;;;;;;;;;;;;;;;;;;;0BAQ5G,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;;kCAE3B,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAgB,OAAO;gCACpC,iBAAiB,CAAC,iQAAiQ,CAAC;gCACpR,kBAAkB;4BACpB;;;;;;;;;;;kCAGF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAG,WAAU;;gDAAgE;8DAE5E,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDAIpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;wDAAmL;sEACrL,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAEtC,8OAAC;oDAAO,WAAU;8DAAkJ;;;;;;;;;;;;sDAMtK,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;8DAA6F;;;;;;;;;;;;sDAI9G,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAK9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;sDAIhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAsI;;;;;;;;;;;;;;;;;8CAO5J,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DAA0F;;;;;;;;;;;;sDAI3G,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAK9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;sDAIhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAA0I;;;;;;;;;;;;;;;;;8CAOhK,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;8DAA4F;;;;;;;;;;;;sDAI7G,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAK9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;;;;;;;8DAEhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;;8DAEjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;sDAIhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAwI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpK,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAA4C;;;;;;sDAGzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkG;;;;;;8DAGjH,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAA4C;;;;;;sDAGzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkG;;;;;;8DAGjH,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAE,WAAU;sDAA4C;;;;;;sDAGzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkG;;;;;;8DAGjH,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnD,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/D,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAoI;;;;;;;;;;;;;;;;;;;;;;0BAQ5J,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA8G;;;;;;8CAGhI,8OAAC;oCAAO,WAAU;8CAAqI;;;;;;;;;;;;sCAIzJ,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}