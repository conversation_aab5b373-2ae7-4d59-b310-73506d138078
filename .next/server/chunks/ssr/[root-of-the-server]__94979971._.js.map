{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/camiweb/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Bike, <PERSON>, Waves, MapPin, Star, Users, Calendar, ArrowRight } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-orange-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-2\">\n              <Bike className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-2xl font-bold text-blue-900\">Camí</span>\n            </div>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Home</a>\n              <a href=\"#tours\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Tours</a>\n              <a href=\"#about\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">About</a>\n              <a href=\"#gallery\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Gallery</a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">Contact</a>\n            </div>\n            <button className=\"bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors\">\n              Book Now\n            </button>\n          </div>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <h1 className=\"text-5xl md:text-7xl font-bold text-blue-900 mb-6\">\n              DISCOVER CATALONIA\n              <br />\n              <span className=\"text-blue-600\">ON TWO WHEELS</span>\n            </h1>\n            <p className=\"text-xl text-gray-700 mb-8 max-w-3xl mx-auto\">\n              Experience the authentic beauty of Catalunya through immersive cycling adventures. \n              From stunning coastlines to majestic mountains, every pedal stroke tells a story.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center\">\n                Explore Tours <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </button>\n              <button className=\"border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors\">\n                Watch Video\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        {/* Hero Image */}\n        <div className=\"mt-16 max-w-6xl mx-auto\">\n          <div className=\"relative h-96 md:h-[500px] rounded-2xl overflow-hidden shadow-2xl\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-orange-500/20\"></div>\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div className=\"text-center text-white\">\n                <Bike className=\"h-24 w-24 mx-auto mb-4 opacity-80\" />\n                <p className=\"text-lg\">Cyclists on coastal road in Catalunya</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Tours */}\n      <section id=\"tours\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-blue-900 mb-4\">Featured Tours</h2>\n            <p className=\"text-xl text-gray-600\">Choose your perfect cycling adventure</p>\n          </div>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {/* Coastal Route */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors\">\n              <div className=\"flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-6 mx-auto\">\n                <Waves className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-4\">Coastal Route</h3>\n              <p className=\"text-gray-700 text-center mb-6\">\n                Ride along Catalunya's breathtaking Mediterranean coastline, discovering hidden coves and charming fishing villages.\n              </p>\n              <div className=\"flex justify-center\">\n                <button className=\"bg-blue-600 text-white px-6 py-3 rounded-full hover:bg-blue-700 transition-colors\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n\n            {/* Mountain Trail */}\n            <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors\">\n              <div className=\"flex items-center justify-center w-16 h-16 bg-orange-600 rounded-full mb-6 mx-auto\">\n                <Mountain className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-4\">Mountain Trail</h3>\n              <p className=\"text-gray-700 text-center mb-6\">\n                Challenge yourself on scenic mountain paths through the Pyrenees, with stunning vistas and authentic mountain culture.\n              </p>\n              <div className=\"flex justify-center\">\n                <button className=\"bg-orange-600 text-white px-6 py-3 rounded-full hover:bg-orange-700 transition-colors\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n\n            {/* Valley Path */}\n            <div className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border-2 border-orange-200 hover:border-orange-400 transition-colors\">\n              <div className=\"flex items-center justify-center w-16 h-16 bg-green-600 rounded-full mb-6 mx-auto\">\n                <MapPin className=\"h-8 w-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-blue-900 text-center mb-4\">Valley Path</h3>\n              <p className=\"text-gray-700 text-center mb-6\">\n                Explore peaceful valleys dotted with vineyards, historic towns, and local markets showcasing Catalunya's rich heritage.\n              </p>\n              <div className=\"flex justify-center\">\n                <button className=\"bg-green-600 text-white px-6 py-3 rounded-full hover:bg-green-700 transition-colors\">\n                  Learn More\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Story */}\n      <section id=\"about\" className=\"py-20 bg-gradient-to-br from-orange-50 to-blue-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            <div>\n              <div className=\"inline-flex items-center bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-semibold mb-6\">\n                <Star className=\"h-4 w-4 mr-2\" />\n                Zas! Our Story\n              </div>\n              <h2 className=\"text-4xl font-bold text-blue-900 mb-6\">\n                Founded by passionate cyclists, we're dedicated to sharing the beauty of Catalonia through unique and memorable bike tours.\n              </h2>\n              <p className=\"text-lg text-gray-700 mb-8\">\n                \"A fantastic way to experience the diverse landscapes of Catalonia!\" - Michael T.\n              </p>\n              <div className=\"grid grid-cols-2 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full mb-3 mx-auto\">\n                    <Users className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-blue-900\">Expert Guides</h4>\n                  <p className=\"text-gray-600 text-sm\">Local knowledge & passion</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-orange-600 rounded-full mb-3 mx-auto\">\n                    <Calendar className=\"h-6 w-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-blue-900\">All Skill Levels</h4>\n                  <p className=\"text-gray-600 text-sm\">Adventures for everyone</p>\n                </div>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"bg-gradient-to-br from-blue-600 to-orange-500 rounded-2xl p-8 text-white\">\n                <h3 className=\"text-2xl font-bold mb-4\">Why Choose Camí?</h3>\n                <ul className=\"space-y-3\">\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Authentic local experiences\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Small group adventures\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Premium equipment included\n                  </li>\n                  <li className=\"flex items-center\">\n                    <Star className=\"h-5 w-5 mr-3 text-yellow-300\" />\n                    Cultural immersion guaranteed\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact */}\n      <section id=\"contact\" className=\"py-20 bg-blue-900 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold mb-6\">Ready for Your Adventure?</h2>\n          <p className=\"text-xl mb-8 max-w-2xl mx-auto\">\n            Join us for an unforgettable cycling experience through the heart of Catalunya. \n            Contact us today to start planning your perfect bike tour.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-8\">\n            <button className=\"bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-orange-700 transition-colors\">\n              Book Your Tour\n            </button>\n            <button className=\"border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors\">\n              Get In Touch\n            </button>\n          </div>\n          <p className=\"text-blue-200\">\n            Email: <EMAIL> | Phone: +34 123 456 789\n          </p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAsD;;;;;;kDAChF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACjF,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDACjF,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAsD;;;;;;kDACnF,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;0CAErF,8OAAC;gCAAO,WAAU;0CAAoF;;;;;;;;;;;;;;;;;;;;;;0BAQ5G,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;;kCAC3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAoD;sDAEhE,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;gDAA2I;8DAC7I,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAEtC,8OAAC;4CAAO,WAAU;sDAA2I;;;;;;;;;;;;;;;;;;;;;;;kCAQnK,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjC,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiC;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAoF;;;;;;;;;;;;;;;;;8CAO1G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiC;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAwF;;;;;;;;;;;;;;;;;8CAO9G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAClE,8OAAC;4CAAE,WAAU;sDAAiC;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlH,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAGnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/D,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA8G;;;;;;8CAGhI,8OAAC;oCAAO,WAAU;8CAAqI;;;;;;;;;;;;sCAIzJ,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}]}